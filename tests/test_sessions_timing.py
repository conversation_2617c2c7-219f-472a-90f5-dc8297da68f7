"""Tests specifically focused on timing and logging for session API endpoints."""

import logging
import time
import uuid
from datetime import datetime
from io import String<PERSON>
from unittest.mock import patch, AsyncMock, MagicMock

import pytest
from fastapi import status

from tests.test_utils import (
    TestDataFactory,
    AuthTest<PERSON>elper,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    PerformanceTestHelper,
    ResponseValidator
)


class TestSessionsTimingAndLogging:
    """Test class focused on timing and logging aspects of sessions API."""

    @pytest.fixture(autouse=True)
    def setup_method(self, client):
        """Setup method for timing tests."""
        self.client = client
        self.base_url = "/chat/v1.0/be"
        
        # Create test data
        self.user_data = TestDataFactory.create_user_data()
        self.auth_token = AuthTestHelper.create_auth_token(self.user_data)
        self.auth_headers = AuthTestHelper.create_auth_headers(self.auth_token)
        
        # Setup logging capture
        self.log_capture = StringIO()
        self.log_handler = logging.StreamHandler(self.log_capture)
        self.log_handler.setLevel(logging.INFO)
        
        # Get logger (adjust logger name based on your app's logging setup)
        self.logger = logging.getLogger("app")
        self.logger.addHandler(self.log_handler)
        self.logger.setLevel(logging.INFO)

    def teardown_method(self):
        """Cleanup after each test."""
        self.logger.removeHandler(self.log_handler)
        self.log_handler.close()

    def test_sessions_endpoint_timing_with_logging(self, mock_db_session):
        """Test sessions endpoint timing with detailed logging."""
        # Create test sessions
        mock_sessions = TestDataFactory.create_multiple_sessions(
            self.user_data["id"], count=10
        )

        with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
            mock_auth.return_value = (MagicMock(id=self.user_data["id"]), "mock_token")
            
            with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                mock_get_session.return_value = mock_sessions
                
                with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                    mock_db.return_value = mock_db_session
                    
                    # Log start time
                    self.logger.info("Starting sessions endpoint test")
                    
                    # Measure response time
                    response, response_time = TimingTestHelper.measure_response_time(
                        self.client.get,
                        f"{self.base_url}/sessions",
                        headers=self.auth_headers
                    )
                    
                    # Log completion
                    self.logger.info(f"Sessions endpoint completed in {response_time:.3f}s")

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        # Validate response structure
        response_data = response.json()
        assert ResponseValidator.validate_sessions_response(response_data)
        
        # Timing assertions
        TimingTestHelper.assert_response_time_within_limit(response_time, 1.0)
        
        # Check logs
        log_contents = self.log_capture.getvalue()
        assert "Starting sessions endpoint test" in log_contents
        assert "Sessions endpoint completed" in log_contents
        assert f"{response_time:.3f}s" in log_contents

    def test_session_history_endpoint_timing_with_logging(self, mock_db_session):
        """Test session history endpoint timing with detailed logging."""
        session_id = uuid.uuid4()
        
        # Create test conversation history
        mock_conversations = TestDataFactory.create_conversation_history(
            session_id, message_count=20
        )

        with patch("app.src.controllers.chatbot_controller.session_service.get_conversation_history") as mock_get_history:
            mock_get_history.return_value = mock_conversations
            
            with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                mock_db.return_value = mock_db_session
                
                # Log start time
                self.logger.info(f"Starting session history test for session {session_id}")
                
                # Measure response time
                response, response_time = TimingTestHelper.measure_response_time(
                    self.client.post,
                    f"{self.base_url}/sessions/{session_id}"
                )
                
                # Log completion
                self.logger.info(f"Session history endpoint completed in {response_time:.3f}s")

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        # Validate response structure
        response_data = response.json()
        assert ResponseValidator.validate_session_history_response(response_data)
        
        # Timing assertions
        TimingTestHelper.assert_response_time_within_limit(response_time, 1.0)
        
        # Check logs
        log_contents = self.log_capture.getvalue()
        assert f"Starting session history test for session {session_id}" in log_contents
        assert "Session history endpoint completed" in log_contents

    def test_performance_benchmarking_sessions(self, mock_db_session):
        """Benchmark performance of sessions endpoint over multiple requests."""
        # Create test data
        mock_sessions = TestDataFactory.create_multiple_sessions(
            self.user_data["id"], count=5
        )

        def make_sessions_request():
            """Make a single sessions request."""
            with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
                mock_auth.return_value = (MagicMock(id=self.user_data["id"]), "mock_token")
                
                with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                    mock_get_session.return_value = mock_sessions
                    
                    with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                        mock_db.return_value = mock_db_session
                        
                        return self.client.get(
                            f"{self.base_url}/sessions",
                            headers=self.auth_headers
                        )

        # Run performance test
        self.logger.info("Starting performance benchmark for sessions endpoint")
        
        response_times = PerformanceTestHelper.run_multiple_requests(
            make_sessions_request, count=10
        )
        
        # Analyze performance
        metrics = PerformanceTestHelper.analyze_performance_metrics(response_times)
        
        # Log performance metrics
        self.logger.info(f"Performance metrics: {metrics}")
        
        # Assert performance requirements
        PerformanceTestHelper.assert_performance_requirements(
            response_times,
            max_avg_time=0.1,  # 100ms average
            max_single_time=0.2  # 200ms max
        )
        
        # Check logs contain performance data
        log_contents = self.log_capture.getvalue()
        assert "Performance metrics:" in log_contents
        assert "Starting performance benchmark" in log_contents

    def test_performance_benchmarking_session_history(self, mock_db_session):
        """Benchmark performance of session history endpoint over multiple requests."""
        session_id = uuid.uuid4()
        mock_conversations = TestDataFactory.create_conversation_history(
            session_id, message_count=10
        )

        def make_history_request():
            """Make a single session history request."""
            with patch("app.src.controllers.chatbot_controller.session_service.get_conversation_history") as mock_get_history:
                mock_get_history.return_value = mock_conversations
                
                with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                    mock_db.return_value = mock_db_session
                    
                    return self.client.post(f"{self.base_url}/sessions/{session_id}")

        # Run performance test
        self.logger.info(f"Starting performance benchmark for session history endpoint")
        
        response_times = PerformanceTestHelper.run_multiple_requests(
            make_history_request, count=10
        )
        
        # Analyze performance
        metrics = PerformanceTestHelper.analyze_performance_metrics(response_times)
        
        # Log performance metrics
        self.logger.info(f"Session history performance metrics: {metrics}")
        
        # Assert performance requirements
        PerformanceTestHelper.assert_performance_requirements(
            response_times,
            max_avg_time=0.1,
            max_single_time=0.2
        )

    def test_timing_under_different_load_conditions(self, mock_db_session):
        """Test timing behavior under different simulated load conditions."""
        test_cases = [
            {"sessions_count": 1, "description": "Light load"},
            {"sessions_count": 10, "description": "Medium load"},
            {"sessions_count": 50, "description": "Heavy load"},
        ]

        for case in test_cases:
            self.logger.info(f"Testing {case['description']} with {case['sessions_count']} sessions")
            
            # Create test data for this case
            mock_sessions = TestDataFactory.create_multiple_sessions(
                self.user_data["id"], count=case["sessions_count"]
            )

            with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
                mock_auth.return_value = (MagicMock(id=self.user_data["id"]), "mock_token")
                
                with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                    mock_get_session.return_value = mock_sessions
                    
                    with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                        mock_db.return_value = mock_db_session
                        
                        # Measure response time
                        response, response_time = TimingTestHelper.measure_response_time(
                            self.client.get,
                            f"{self.base_url}/sessions",
                            headers=self.auth_headers
                        )

            # Log results
            self.logger.info(f"{case['description']} completed in {response_time:.3f}s")
            
            # Assertions
            assert response.status_code == status.HTTP_200_OK
            
            # Response time should scale reasonably with load
            if case["sessions_count"] <= 10:
                TimingTestHelper.assert_response_time_within_limit(response_time, 0.5)
            else:
                TimingTestHelper.assert_response_time_within_limit(response_time, 1.0)

    def test_middleware_timing_integration(self, mock_db_session):
        """Test integration with timer middleware for request timing."""
        # Note: This test assumes the timer_middleware is logging request times
        # You may need to adjust based on your actual middleware implementation
        
        mock_sessions = TestDataFactory.create_multiple_sessions(
            self.user_data["id"], count=3
        )

        with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
            mock_auth.return_value = (MagicMock(id=self.user_data["id"]), "mock_token")
            
            with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                mock_get_session.return_value = mock_sessions
                
                with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                    mock_db.return_value = mock_db_session
                    
                    # Make request - middleware should log timing
                    response = self.client.get(
                        f"{self.base_url}/sessions",
                        headers=self.auth_headers
                    )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        # The timer_middleware should have added timing information
        # This might be in response headers or logs depending on implementation
        # Adjust assertions based on your middleware behavior

    def test_error_response_timing(self, mock_db_session):
        """Test timing of error responses."""
        # Test unauthorized request timing
        start_time = time.time()
        response = self.client.get(f"{self.base_url}/sessions")
        unauthorized_time = time.time() - start_time
        
        self.logger.info(f"Unauthorized request completed in {unauthorized_time:.3f}s")
        
        # Test invalid session ID timing
        invalid_session_id = "invalid-uuid"
        start_time = time.time()
        response = self.client.post(f"{self.base_url}/sessions/{invalid_session_id}")
        invalid_id_time = time.time() - start_time
        
        self.logger.info(f"Invalid session ID request completed in {invalid_id_time:.3f}s")
        
        # Error responses should be fast
        assert unauthorized_time < 0.5
        assert invalid_id_time < 0.5
        
        # Check logs
        log_contents = self.log_capture.getvalue()
        assert "Unauthorized request completed" in log_contents
        assert "Invalid session ID request completed" in log_contents
