"""Tests for session API endpoints (/be/sessions and /be/sessions/{session_id})."""

import json
import time
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, patch, MagicMock

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from app.src.schemas.session_status import ResponseObject


class TestSessionsAPI:
    """Test class for sessions API endpoints."""

    @pytest.fixture(autouse=True)
    def setup_method(self, client, auth_headers, test_user, test_chat_session):
        """Setup method for each test."""
        self.client = client
        self.auth_headers = auth_headers
        self.test_user = test_user
        self.test_chat_session = test_chat_session
        self.base_url = "/chat/v1.0/be"

    def test_get_sessions_success(self, mock_db_session):
        """Test successful retrieval of user sessions."""
        # Mock session service response
        mock_sessions = [
            (
                self.test_chat_session.session_id,
                self.test_chat_session.summary_session,
                self.test_chat_session.created_at,
            )
        ]

        with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
            mock_auth.return_value = (self.test_user, "mock_token")
            
            with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                mock_get_session.return_value = mock_sessions
                
                with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                    mock_db.return_value = mock_db_session
                    
                    # Measure response time
                    start_time = time.time()
                    response = self.client.get(
                        f"{self.base_url}/sessions",
                        headers=self.auth_headers
                    )
                    end_time = time.time()
                    response_time = end_time - start_time

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["code"] == "SS0000"
        assert response_data["message"] == ""
        assert "sessions" in response_data["data"]
        
        sessions = response_data["data"]["sessions"]
        assert len(sessions) == 1
        assert sessions[0]["session_id"] == str(self.test_chat_session.session_id)
        assert sessions[0]["summary_session"] == self.test_chat_session.summary_session
        assert "created_at" in sessions[0]
        
        # Verify response time is reasonable (less than 1 second for mocked response)
        assert response_time < 1.0
        
        # Verify service was called with correct parameters
        mock_get_session.assert_called_once_with(mock_db_session, self.test_user.id)

    def test_get_sessions_no_sessions_found(self, mock_db_session):
        """Test when user has no sessions."""
        with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
            mock_auth.return_value = (self.test_user, "mock_token")
            
            with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                mock_get_session.return_value = None
                
                with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                    mock_db.return_value = mock_db_session
                    
                    response = self.client.get(
                        f"{self.base_url}/sessions",
                        headers=self.auth_headers
                    )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["code"] == "SS0001"
        assert response_data["message"] == "Session not found"
        assert "error" in response_data["data"]
        assert response_data["data"]["error"] == "User has no session chat"

    def test_get_sessions_unauthorized(self):
        """Test unauthorized access to sessions endpoint."""
        response = self.client.get(f"{self.base_url}/sessions")
        
        # Should return 401 or 403 for unauthorized access
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]

    def test_get_sessions_invalid_token(self):
        """Test with invalid authorization token."""
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        
        with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
            mock_auth.side_effect = Exception("Invalid token")
            
            response = self.client.get(
                f"{self.base_url}/sessions",
                headers=invalid_headers
            )
        
        # Should return error status
        assert response.status_code >= 400

    def test_get_session_history_success(self, mock_db_session, test_conversation_data):
        """Test successful retrieval of session history."""
        session_id = str(self.test_chat_session.session_id)
        
        # Mock conversation history
        mock_history = [
            MagicMock(
                content="What is AI?",
                timestamp=datetime.now()
            ),
            MagicMock(
                content="AI stands for Artificial Intelligence...",
                timestamp=datetime.now()
            )
        ]

        with patch("app.src.controllers.chatbot_controller.session_service.get_conversation_history") as mock_get_history:
            mock_get_history.return_value = mock_history
            
            with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                mock_db.return_value = mock_db_session
                
                # Measure response time
                start_time = time.time()
                response = self.client.post(f"{self.base_url}/sessions/{session_id}")
                end_time = time.time()
                response_time = end_time - start_time

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["code"] == "HST0000"
        assert response_data["message"] == ""
        assert "history" in response_data["data"]
        
        history = response_data["data"]["history"]
        assert len(history) == 1  # Should have one Q&A pair
        assert "question" in history[0]
        assert "response" in history[0]
        assert "timestamp" in history[0]
        
        # Verify response time
        assert response_time < 1.0
        
        # Verify service was called with correct session ID
        mock_get_history.assert_called_once()
        call_args = mock_get_history.call_args[0]
        assert call_args[1] == uuid.UUID(session_id)

    def test_get_session_history_no_conversations(self, mock_db_session):
        """Test when session has no conversations."""
        session_id = str(self.test_chat_session.session_id)

        with patch("app.src.controllers.chatbot_controller.session_service.get_conversation_history") as mock_get_history:
            mock_get_history.return_value = []
            
            with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                mock_db.return_value = mock_db_session
                
                response = self.client.post(f"{self.base_url}/sessions/{session_id}")

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["code"] == "HST0002"
        assert response_data["message"] == ""
        assert "error" in response_data["data"]
        assert response_data["data"]["error"] == "No conversations in this session"

    def test_get_session_history_invalid_session_id(self, mock_db_session):
        """Test with invalid session ID format."""
        invalid_session_id = "invalid-uuid"
        
        response = self.client.post(f"{self.base_url}/sessions/{invalid_session_id}")
        
        # Should return error for invalid UUID format
        assert response.status_code >= 400

    def test_get_session_history_nonexistent_session(self, mock_db_session):
        """Test with non-existent session ID."""
        nonexistent_session_id = str(uuid.uuid4())

        with patch("app.src.controllers.chatbot_controller.session_service.get_conversation_history") as mock_get_history:
            mock_get_history.return_value = []
            
            with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                mock_db.return_value = mock_db_session
                
                response = self.client.post(f"{self.base_url}/sessions/{nonexistent_session_id}")

        # Should return empty history or error
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["code"] == "HST0002"


class TestSessionsAPITiming:
    """Test class for timing and performance aspects of sessions API."""

    @pytest.fixture(autouse=True)
    def setup_method(self, client, auth_headers, test_user):
        """Setup method for timing tests."""
        self.client = client
        self.auth_headers = auth_headers
        self.test_user = test_user
        self.base_url = "/chat/v1.0/be"

    def test_sessions_endpoint_response_time(self, mock_db_session):
        """Test that sessions endpoint responds within acceptable time limits."""
        with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
            mock_auth.return_value = (self.test_user, "mock_token")
            
            with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                mock_get_session.return_value = []
                
                with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                    mock_db.return_value = mock_db_session
                    
                    # Measure multiple requests to get average response time
                    response_times = []
                    for _ in range(5):
                        start_time = time.time()
                        response = self.client.get(
                            f"{self.base_url}/sessions",
                            headers=self.auth_headers
                        )
                        end_time = time.time()
                        response_times.append(end_time - start_time)
                        
                        assert response.status_code == status.HTTP_200_OK

        # Calculate average response time
        avg_response_time = sum(response_times) / len(response_times)
        
        # Assert average response time is under 100ms for mocked responses
        assert avg_response_time < 0.1
        
        # Assert no single request took longer than 200ms
        assert all(rt < 0.2 for rt in response_times)

    def test_session_history_endpoint_response_time(self, mock_db_session):
        """Test that session history endpoint responds within acceptable time limits."""
        session_id = str(uuid.uuid4())
        
        with patch("app.src.controllers.chatbot_controller.session_service.get_conversation_history") as mock_get_history:
            mock_get_history.return_value = []
            
            with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                mock_db.return_value = mock_db_session
                
                # Measure response time
                start_time = time.time()
                response = self.client.post(f"{self.base_url}/sessions/{session_id}")
                end_time = time.time()
                response_time = end_time - start_time

        assert response.status_code == status.HTTP_200_OK
        
        # Assert response time is under 100ms for mocked response
        assert response_time < 0.1
