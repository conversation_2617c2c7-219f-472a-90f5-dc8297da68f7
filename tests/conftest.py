import asyncio
import uuid
from datetime import datetime
from os.path import dirname, join
from typing import Async<PERSON>enerator, Dict, Any
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from decouple import Config, RepositoryEnv
from fastapi.testclient import TestClient
from fastapi_base.authen.bearer import jwt_encode
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.src.app import app
from app.src.models import User, ChatSession, ChatConversation
from app.src.utils.wrapper_db_session import get_db_session

PATH_ENV = join(dirname(__file__), "..", ".env")
decouple_config = Config(RepositoryEnv(PATH_ENV))

# Test database URL - using SQLite for testing
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"

# Create test engine
test_engine = create_async_engine(TEST_DATABASE_URL, echo=True)
TestSessionLocal = sessionmaker(
    test_engine, class_=AsyncSession, expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    async with TestSessionLocal() as session:
        yield session


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    return AsyncMock(spec=AsyncSession)


@pytest.fixture
def test_user_data() -> Dict[str, Any]:
    """Test user data."""
    return {
        "id": uuid.uuid4(),
        "email": "<EMAIL>",
        "password": "hashed_password",
        "is_deleted": False,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
    }


@pytest.fixture
def test_user(test_user_data) -> User:
    """Create a test user instance."""
    user = User()
    for key, value in test_user_data.items():
        setattr(user, key, value)
    return user


@pytest.fixture
def auth_token(test_user_data) -> str:
    """Generate a valid JWT token for testing."""
    user_data = {
        "user": {
            "user_id": str(test_user_data["id"]),
            "email": test_user_data["email"],
        }
    }
    return jwt_encode("", user_data)


@pytest.fixture
def auth_headers(auth_token) -> Dict[str, str]:
    """Create authorization headers."""
    return {"Authorization": f"Bearer {auth_token}"}


@pytest.fixture
def test_session_data(test_user_data) -> Dict[str, Any]:
    """Test session data."""
    return {
        "session_id": uuid.uuid4(),
        "id": uuid.uuid4(),
        "user_id": test_user_data["id"],
        "summary_session": "Test session summary",
        "summary_conversation": "Test conversation summary",
        "created_at": datetime.now(),
        "is_deleted": False,
    }


@pytest.fixture
def test_chat_session(test_session_data) -> ChatSession:
    """Create a test chat session instance."""
    session = ChatSession()
    for key, value in test_session_data.items():
        setattr(session, key, value)
    return session


@pytest.fixture
def test_conversation_data(test_session_data) -> Dict[str, Any]:
    """Test conversation data."""
    return {
        "id": uuid.uuid4(),
        "session_id": test_session_data["session_id"],
        "role": "user",
        "content": "Test message",
        "timestamp": datetime.now(),
        "is_deleted": False,
        "is_summarized": False,
    }


@pytest.fixture
def test_chat_conversation(test_conversation_data) -> ChatConversation:
    """Create a test chat conversation instance."""
    conversation = ChatConversation()
    for key, value in test_conversation_data.items():
        setattr(conversation, key, value)
    return conversation


def override_get_db_session():
    """Override database session for testing."""
    return AsyncMock(spec=AsyncSession)
