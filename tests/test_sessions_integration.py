"""Integration tests for session API endpoints with database interactions."""

import time
import uuid
from datetime import datetime
from unittest.mock import patch, AsyncMock

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from app.src.models import ChatSession, ChatConversation
from app.src.schemas.chat_sessions import ChatSessionCreate, ChatConversationCreate


class TestSessionsIntegration:
    """Integration tests for sessions API with database operations."""

    @pytest.fixture(autouse=True)
    def setup_method(self, client, auth_headers, test_user):
        """Setup method for integration tests."""
        self.client = client
        self.auth_headers = auth_headers
        self.test_user = test_user
        self.base_url = "/chat/v1.0/be"

    @pytest.mark.asyncio
    async def test_get_sessions_with_database_timing(self, mock_db_session):
        """Test sessions endpoint with simulated database timing."""
        # Create test session data
        session_id = uuid.uuid4()
        created_at = datetime.now()
        
        mock_sessions = [
            (session_id, "Test session summary", created_at)
        ]

        # Mock database operations with realistic timing
        async def mock_get_session_with_delay(*args, **kwargs):
            # Simulate database query time (10-50ms)
            await AsyncMock(return_value=None)()
            return mock_sessions

        with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
            mock_auth.return_value = (self.test_user, "mock_token")
            
            with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                mock_get_session.side_effect = mock_get_session_with_delay
                
                with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                    mock_db.return_value = mock_db_session
                    
                    # Measure total response time including simulated DB operations
                    start_time = time.time()
                    response = self.client.get(
                        f"{self.base_url}/sessions",
                        headers=self.auth_headers
                    )
                    end_time = time.time()
                    total_response_time = end_time - start_time

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["code"] == "SS0000"
        assert "sessions" in response_data["data"]
        
        sessions = response_data["data"]["sessions"]
        assert len(sessions) == 1
        assert sessions[0]["session_id"] == str(session_id)
        
        # Verify total response time is reasonable (should be under 1 second even with DB simulation)
        assert total_response_time < 1.0

    @pytest.mark.asyncio
    async def test_get_session_history_with_database_timing(self, mock_db_session):
        """Test session history endpoint with simulated database timing."""
        session_id = uuid.uuid4()
        
        # Create mock conversation history
        mock_conversations = [
            AsyncMock(
                content="What is machine learning?",
                timestamp=datetime.now(),
                role="user"
            ),
            AsyncMock(
                content="Machine learning is a subset of AI...",
                timestamp=datetime.now(),
                role="assistant"
            )
        ]

        # Mock database operations with realistic timing
        async def mock_get_history_with_delay(*args, **kwargs):
            # Simulate database query time
            await AsyncMock(return_value=None)()
            return mock_conversations

        with patch("app.src.controllers.chatbot_controller.session_service.get_conversation_history") as mock_get_history:
            mock_get_history.side_effect = mock_get_history_with_delay
            
            with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                mock_db.return_value = mock_db_session
                
                # Measure total response time
                start_time = time.time()
                response = self.client.post(f"{self.base_url}/sessions/{session_id}")
                end_time = time.time()
                total_response_time = end_time - start_time

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["code"] == "HST0000"
        assert "history" in response_data["data"]
        
        history = response_data["data"]["history"]
        assert len(history) == 1  # One Q&A pair
        
        # Verify response time
        assert total_response_time < 1.0

    def test_sessions_endpoint_with_large_dataset(self, mock_db_session):
        """Test sessions endpoint performance with large number of sessions."""
        # Create a large number of mock sessions
        mock_sessions = []
        for i in range(100):  # 100 sessions
            session_id = uuid.uuid4()
            created_at = datetime.now()
            mock_sessions.append((session_id, f"Session {i} summary", created_at))

        with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
            mock_auth.return_value = (self.test_user, "mock_token")
            
            with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                mock_get_session.return_value = mock_sessions
                
                with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                    mock_db.return_value = mock_db_session
                    
                    # Measure response time with large dataset
                    start_time = time.time()
                    response = self.client.get(
                        f"{self.base_url}/sessions",
                        headers=self.auth_headers
                    )
                    end_time = time.time()
                    response_time = end_time - start_time

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["code"] == "SS0000"
        assert "sessions" in response_data["data"]
        
        sessions = response_data["data"]["sessions"]
        assert len(sessions) == 100
        
        # Verify response time is still reasonable with large dataset
        assert response_time < 2.0  # Allow more time for large dataset processing

    def test_session_history_with_large_conversation(self, mock_db_session):
        """Test session history endpoint with large conversation history."""
        session_id = uuid.uuid4()
        
        # Create a large conversation history (100 messages = 50 Q&A pairs)
        mock_conversations = []
        for i in range(100):
            role = "user" if i % 2 == 0 else "assistant"
            content = f"Message {i} content"
            mock_conversations.append(
                AsyncMock(
                    content=content,
                    timestamp=datetime.now(),
                    role=role
                )
            )

        with patch("app.src.controllers.chatbot_controller.session_service.get_conversation_history") as mock_get_history:
            mock_get_history.return_value = mock_conversations
            
            with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                mock_db.return_value = mock_db_session
                
                # Measure response time with large conversation
                start_time = time.time()
                response = self.client.post(f"{self.base_url}/sessions/{session_id}")
                end_time = time.time()
                response_time = end_time - start_time

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        
        response_data = response.json()
        assert response_data["code"] == "HST0000"
        assert "history" in response_data["data"]
        
        history = response_data["data"]["history"]
        assert len(history) == 50  # 50 Q&A pairs from 100 messages
        
        # Verify response time is reasonable even with large conversation
        assert response_time < 2.0


class TestSessionsAPIErrorHandling:
    """Test error handling and edge cases for sessions API."""

    @pytest.fixture(autouse=True)
    def setup_method(self, client, auth_headers, test_user):
        """Setup method for error handling tests."""
        self.client = client
        self.auth_headers = auth_headers
        self.test_user = test_user
        self.base_url = "/chat/v1.0/be"

    def test_sessions_database_error_handling(self, mock_db_session):
        """Test handling of database errors in sessions endpoint."""
        with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
            mock_auth.return_value = (self.test_user, "mock_token")
            
            with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                # Simulate database error
                mock_get_session.side_effect = Exception("Database connection error")
                
                with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                    mock_db.return_value = mock_db_session
                    
                    response = self.client.get(
                        f"{self.base_url}/sessions",
                        headers=self.auth_headers
                    )

        # Should handle database errors gracefully
        assert response.status_code >= 500

    def test_session_history_database_error_handling(self, mock_db_session):
        """Test handling of database errors in session history endpoint."""
        session_id = str(uuid.uuid4())

        with patch("app.src.controllers.chatbot_controller.session_service.get_conversation_history") as mock_get_history:
            # Simulate database error
            mock_get_history.side_effect = Exception("Database connection error")
            
            with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                mock_db.return_value = mock_db_session
                
                response = self.client.post(f"{self.base_url}/sessions/{session_id}")

        # Should handle database errors gracefully
        assert response.status_code >= 500

    def test_concurrent_requests_timing(self, mock_db_session):
        """Test timing behavior under concurrent requests simulation."""
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def make_request():
            """Make a request and record timing."""
            with patch("app.src.controllers.chatbot_controller.user_service.get_current_user") as mock_auth:
                mock_auth.return_value = (self.test_user, "mock_token")
                
                with patch("app.src.controllers.chatbot_controller.session_service.get_session") as mock_get_session:
                    mock_get_session.return_value = []
                    
                    with patch("app.src.controllers.chatbot_controller.get_db_session") as mock_db:
                        mock_db.return_value = mock_db_session
                        
                        start_time = time.time()
                        response = self.client.get(
                            f"{self.base_url}/sessions",
                            headers=self.auth_headers
                        )
                        end_time = time.time()
                        
                        results_queue.put({
                            'status_code': response.status_code,
                            'response_time': end_time - start_time
                        })

        # Create multiple threads to simulate concurrent requests
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Collect results
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())

        # Assertions
        assert len(results) == 5
        
        # All requests should succeed
        for result in results:
            assert result['status_code'] == status.HTTP_200_OK
            # Response time should still be reasonable under concurrent load
            assert result['response_time'] < 1.0
