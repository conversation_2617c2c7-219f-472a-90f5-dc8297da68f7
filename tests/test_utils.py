"""Test utilities for session API testing."""

import time
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from unittest.mock import AsyncMock, MagicMock

from fastapi_base.authen.bearer import jwt_encode

from app.src.models import User, ChatSession, ChatConversation


class TestDataFactory:
    """Factory class for creating test data."""

    @staticmethod
    def create_user_data(
        user_id: Optional[uuid.UUID] = None,
        email: str = "<EMAIL>",
        password: str = "hashed_password"
    ) -> Dict[str, Any]:
        """Create test user data."""
        return {
            "id": user_id or uuid.uuid4(),
            "email": email,
            "password": password,
            "is_deleted": False,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }

    @staticmethod
    def create_session_data(
        session_id: Optional[uuid.UUID] = None,
        user_id: Optional[uuid.UUID] = None,
        summary_session: str = "Test session summary",
        summary_conversation: str = "Test conversation summary"
    ) -> Dict[str, Any]:
        """Create test session data."""
        return {
            "session_id": session_id or uuid.uuid4(),
            "id": uuid.uuid4(),
            "user_id": user_id or uuid.uuid4(),
            "summary_session": summary_session,
            "summary_conversation": summary_conversation,
            "created_at": datetime.now(),
            "is_deleted": False,
        }

    @staticmethod
    def create_conversation_data(
        session_id: Optional[uuid.UUID] = None,
        role: str = "user",
        content: str = "Test message"
    ) -> Dict[str, Any]:
        """Create test conversation data."""
        return {
            "id": uuid.uuid4(),
            "session_id": session_id or uuid.uuid4(),
            "role": role,
            "content": content,
            "timestamp": datetime.now(),
            "is_deleted": False,
            "is_summarized": False,
        }

    @staticmethod
    def create_multiple_sessions(
        user_id: uuid.UUID,
        count: int = 5
    ) -> List[tuple]:
        """Create multiple test sessions for a user."""
        sessions = []
        for i in range(count):
            session_id = uuid.uuid4()
            summary = f"Session {i+1} summary"
            created_at = datetime.now()
            sessions.append((session_id, summary, created_at))
        return sessions

    @staticmethod
    def create_conversation_history(
        session_id: uuid.UUID,
        message_count: int = 10
    ) -> List[MagicMock]:
        """Create a conversation history with alternating user/assistant messages."""
        conversations = []
        for i in range(message_count):
            role = "user" if i % 2 == 0 else "assistant"
            content = f"Message {i+1} content"
            
            mock_conversation = MagicMock()
            mock_conversation.content = content
            mock_conversation.timestamp = datetime.now()
            mock_conversation.role = role
            
            conversations.append(mock_conversation)
        
        return conversations


class AuthTestHelper:
    """Helper class for authentication in tests."""

    @staticmethod
    def create_auth_token(user_data: Dict[str, Any]) -> str:
        """Create a valid JWT token for testing."""
        token_data = {
            "user": {
                "user_id": str(user_data["id"]),
                "email": user_data["email"],
            }
        }
        return jwt_encode("", token_data)

    @staticmethod
    def create_auth_headers(token: str) -> Dict[str, str]:
        """Create authorization headers."""
        return {"Authorization": f"Bearer {token}"}

    @staticmethod
    def create_invalid_auth_headers() -> Dict[str, str]:
        """Create invalid authorization headers."""
        return {"Authorization": "Bearer invalid_token"}


class TimingTestHelper:
    """Helper class for timing-related tests."""

    @staticmethod
    def measure_response_time(func, *args, **kwargs) -> tuple:
        """Measure the response time of a function call."""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        response_time = end_time - start_time
        return result, response_time

    @staticmethod
    async def measure_async_response_time(func, *args, **kwargs) -> tuple:
        """Measure the response time of an async function call."""
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()
        response_time = end_time - start_time
        return result, response_time

    @staticmethod
    def assert_response_time_within_limit(response_time: float, limit: float = 1.0):
        """Assert that response time is within acceptable limits."""
        assert response_time < limit, f"Response time {response_time:.3f}s exceeds limit {limit}s"

    @staticmethod
    def calculate_average_response_time(response_times: List[float]) -> float:
        """Calculate average response time from a list of times."""
        return sum(response_times) / len(response_times) if response_times else 0.0


class MockHelper:
    """Helper class for creating mocks."""

    @staticmethod
    def create_mock_db_session() -> AsyncMock:
        """Create a mock database session."""
        return AsyncMock()

    @staticmethod
    def create_mock_user(user_data: Dict[str, Any]) -> MagicMock:
        """Create a mock user object."""
        user = MagicMock(spec=User)
        for key, value in user_data.items():
            setattr(user, key, value)
        return user

    @staticmethod
    def create_mock_session(session_data: Dict[str, Any]) -> MagicMock:
        """Create a mock session object."""
        session = MagicMock(spec=ChatSession)
        for key, value in session_data.items():
            setattr(session, key, value)
        return session

    @staticmethod
    def create_mock_conversation(conversation_data: Dict[str, Any]) -> MagicMock:
        """Create a mock conversation object."""
        conversation = MagicMock(spec=ChatConversation)
        for key, value in conversation_data.items():
            setattr(conversation, key, value)
        return conversation


class ResponseValidator:
    """Helper class for validating API responses."""

    @staticmethod
    def validate_sessions_response(response_data: Dict[str, Any]) -> bool:
        """Validate the structure of a sessions API response."""
        required_fields = ["code", "message", "data"]
        
        # Check top-level fields
        for field in required_fields:
            if field not in response_data:
                return False
        
        # Check success response structure
        if response_data["code"] == "SS0000":
            if "sessions" not in response_data["data"]:
                return False
            
            # Validate each session in the list
            for session in response_data["data"]["sessions"]:
                session_fields = ["session_id", "summary_session", "created_at"]
                for field in session_fields:
                    if field not in session:
                        return False
        
        return True

    @staticmethod
    def validate_session_history_response(response_data: Dict[str, Any]) -> bool:
        """Validate the structure of a session history API response."""
        required_fields = ["code", "message", "data"]
        
        # Check top-level fields
        for field in required_fields:
            if field not in response_data:
                return False
        
        # Check success response structure
        if response_data["code"] == "HST0000":
            if "history" not in response_data["data"]:
                return False
            
            # Validate each history item
            for history_item in response_data["data"]["history"]:
                history_fields = ["question", "response", "timestamp"]
                for field in history_fields:
                    if field not in history_item:
                        return False
        
        return True

    @staticmethod
    def validate_error_response(response_data: Dict[str, Any], expected_code: str) -> bool:
        """Validate the structure of an error response."""
        required_fields = ["code", "message", "data"]
        
        # Check top-level fields
        for field in required_fields:
            if field not in response_data:
                return False
        
        # Check error code matches expected
        if response_data["code"] != expected_code:
            return False
        
        # Check error data structure
        if "error" not in response_data["data"]:
            return False
        
        return True


class PerformanceTestHelper:
    """Helper class for performance testing."""

    @staticmethod
    def run_multiple_requests(func, count: int = 5) -> List[float]:
        """Run a function multiple times and return response times."""
        response_times = []
        for _ in range(count):
            _, response_time = TimingTestHelper.measure_response_time(func)
            response_times.append(response_time)
        return response_times

    @staticmethod
    def analyze_performance_metrics(response_times: List[float]) -> Dict[str, float]:
        """Analyze performance metrics from response times."""
        if not response_times:
            return {}
        
        return {
            "min": min(response_times),
            "max": max(response_times),
            "avg": sum(response_times) / len(response_times),
            "total": sum(response_times)
        }

    @staticmethod
    def assert_performance_requirements(
        response_times: List[float],
        max_avg_time: float = 0.1,
        max_single_time: float = 0.2
    ):
        """Assert that performance requirements are met."""
        metrics = PerformanceTestHelper.analyze_performance_metrics(response_times)
        
        assert metrics["avg"] < max_avg_time, f"Average response time {metrics['avg']:.3f}s exceeds {max_avg_time}s"
        assert metrics["max"] < max_single_time, f"Max response time {metrics['max']:.3f}s exceeds {max_single_time}s"
