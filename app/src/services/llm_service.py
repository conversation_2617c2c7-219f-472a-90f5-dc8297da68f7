import csv
import json
import os
from tempfile import gettempdir
from typing import Any, Async<PERSON><PERSON>ator, Dict, List, Optional, Callable
from uuid import uuid4

import decouple
from fastapi import HTTPException
from openai import APIError, AsyncOpenAI, AuthenticationError, RateLimitError

from app.src.exceptions.error_code import <PERSON><PERSON><PERSON><PERSON><PERSON>rCode
from app.src.schemas.chat_sessions import <PERSON>t<PERSON><PERSON>oryItem
from app.src.services.prompt_service import PromptService
from app.src.services.search_service import SearchService

TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "web_search",
            "description": "Search the web for information about companies, products, or websites, especially to find advertising context or homepage content.",
            "parameters": {"type": "object", "properties": {"query": {"type": "string"}}, "required": ["query"]},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "create_csv_file",
            "description": "Creates a downloadable CSV file from the given content.",
            "parameters": {
                "type": "object",
                "properties": {
                    "content": {"type": "string", "description": "The content to store in CSV, plain text, line by line or comma separated."}
                },
                "required": ["content"]
            },
        }
    }
]

MODEL = decouple.config("MODEL")


class LLMService:
    def __init__(self, search_service: Optional[SearchService] = None, prompt_service: Optional[PromptService] = None):
        self.openai_client = AsyncOpenAI(api_key=decouple.config("OPENAI_API_KEY", default=None))
        self.search_service = search_service
        self.prompt_service = prompt_service

    async def generate_stream_response(
        self,
        request: Any,
        chat_history: List[ChatHistoryItem],
        max_token: int = 1024,
        temperature: int = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        try:
            system_prompt = await self.prompt_service._get_prompts()
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        # print("System prompt: {}".format(system_prompt))
        messages = [{"role": "system", "content": system_prompt}]

        for item in chat_history:
            messages.append({"role": item.role, "content": item.content})
        messages.append({"role": "user", "content": request.question})

        try:
            return self._execute_stream(messages, max_token, temperature, on_tool_call)

        except APIError:
            raise ChatbotErrorCode.RESPONSE_NOT_AVAILABLE.value
        except RateLimitError:
            raise ChatbotErrorCode.REQUEST_DENIED.value
        except AuthenticationError:
            raise ChatbotErrorCode.AUTHENTICATION_FAILED.value
        except Exception:
            raise ChatbotErrorCode.RESPONSE_NOT_AVAILABLE.value

    async def _execute_stream(
        self,
        messages: List[Dict[str, Any]],
        max_token: int = 1024,
        temperature: int = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        response = await self.openai_client.chat.completions.create(
            model=MODEL,
            messages=messages,
            max_tokens=max_token,
            temperature=temperature,
            tools=TOOLS,
            tool_choice="auto",
            stream=True,
        )

        tool_calls = []
        streaming_content = ""

        async for chunk in response:
            choice = chunk.choices[0]
            delta = choice.delta

            if delta.content:
                streaming_content += delta.content
                yield delta.content

            if delta.tool_calls:
                for tcchunk in delta.tool_calls:
                    index = tcchunk.index
                    while len(tool_calls) <= index:
                        tool_calls.append(
                            {
                                "id": "",
                                "type": "function",
                                "function": {"name": "", "arguments": ""},
                            }
                        )

                    tool_call = tool_calls[index]
                    tool_call["id"] += tcchunk.id or ""
                    tool_call["function"]["name"] += tcchunk.function.name or ""
                    tool_call["function"]["arguments"] += tcchunk.function.arguments or ""

            if choice.finish_reason in {"stop", "tool_calls"}:
                break

        if tool_calls:
            messages.append({"role": "assistant", "tool_calls": tool_calls})
            for tool_call in tool_calls:
                tool_name = tool_call["function"]["name"]
                if on_tool_call:
                    await on_tool_call(tool_name)

                if tool_call["function"]["name"] == "web_search":
                    args = json.loads(tool_call["function"]["arguments"])
                    search_result = await self.search_service.search(args["query"])

                    messages.append(
                        {
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "name": "web_search",
                            "content": json.dumps(search_result, ensure_ascii=False),
                        }
                    )

                elif tool_call["function"]["name"] == "create_csv_file":
                    args = json.loads(tool_call["function"]["arguments"])
                    csv_text = args["content"]

                    filename = f"gen_csv_{uuid4().hex}.csv"
                    file_path = os.path.join(gettempdir(), filename)

                    with open(file_path, "w", encoding="utf-8", newline="") as f:
                        writer = csv.writer(f)
                        for line in csv_text.strip().split("\n"):
                            writer.writerow([cell.strip() for cell in line.split(",")])

                    download_url = f"/be/chat/download-csv?filename={filename}"

                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call["id"],
                        "name": "create_csv_file",
                        "content": json.dumps({"download_url": download_url})
                    })

            followup_response = await self.openai_client.chat.completions.create(
                model=MODEL,
                messages=messages,
                temperature=temperature,
                max_tokens=max_token,
                stream=True,
            )

            async for chunk in followup_response:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content

    async def summary_chat_session(self):
        ...

    @staticmethod
    def should_update_summary(chat_history: list) -> bool:
        return len(chat_history) == 0 or len(chat_history) % 20 == 0

    @staticmethod
    def extract_questions(chat_history: list, current_question: str) -> list[str]:
        if not chat_history:
            return [current_question]
        questions = [msg.content for i, msg in enumerate(chat_history) if i % 2 == 0]
        questions.append(current_question)
        return questions



