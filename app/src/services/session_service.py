import uuid
from typing import List, Optional

import decouple
import tiktoken
from fastapi_base.error_code import Server<PERSON>rrorCode
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.src.models.chat_sessions import Chat<PERSON>onversation, ChatSession
from app.src.repositories.chat_session import ChatConversationRepository, ChatSessionRepository
from app.src.schemas.chat_sessions import (
    ChatConversationCreate,
    ChatHistoryItem,
    ChatSessionCreate,
    ChatSessionResponse,
)


class SessionService:
    def __init__(self):
        self.chat_session_repository = ChatSessionRepository(ChatSession)
        self.chat_conversation_repository = ChatConversationRepository(ChatConversation)

    @staticmethod
    def count_tokens(text: str, model: str = "gpt-4.1-mini") -> int:
        try:
            enc = tiktoken.encoding_for_model(model)
        except KeyError:
            enc = tiktoken.get_encoding("cl100k_base")
        return len(enc.encode(text))

    async def create_session(self, db_session: AsyncSession, user_id: uuid.UUID) -> uuid.UUID:
        session_id = str(uuid.uuid4())

        session_data = ChatSessionCreate(session_id=session_id, user_id=user_id, summary_session="", summary_conversation="")
        session = await self.chat_session_repository.create(db_session, obj_in=session_data)
        return session.session_id

    async def get_session(self, db_session: AsyncSession, user_id: uuid.UUID) -> Optional[ChatSessionResponse]:
        try:
            return await self.chat_session_repository.get_session_by_user(db_session, user_id)
        except SQLAlchemyError as ex:
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def add_conversation(
        self, db_session: AsyncSession, session_id: uuid.UUID, question: str, response: str
    ) -> bool:
        try:
            # session = await self.get_session(db_session, session_id)
            # if not session or session.is_deleted:
            #     return False

            # await self.chat_session_repository.update_last_activity(db_session, session_id)

            user_conversation = ChatConversationCreate(
                session_id=session_id,
                role="user",
                content=question,
            )
            await self.chat_conversation_repository.create(db_session, obj_in=user_conversation)
            assistant_conversation = ChatConversationCreate(
                session_id=session_id,
                role="assistant",
                content=response,
            )
            await self.chat_conversation_repository.create(db_session, obj_in=assistant_conversation)
            return True
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def get_conversation_history(self, db_session: AsyncSession, session_id: uuid.UUID) -> List[ChatHistoryItem]:
        try:
            conversations = await self.chat_conversation_repository.get_conversations_by_session_id(
                db_session, session_id
            )
            return [
                ChatHistoryItem(role=conv.role, content=conv.content, timestamp=conv.timestamp)
                for conv in conversations
                if not conv.is_deleted and not conv.is_summarized
            ]
        except SQLAlchemyError as ex:
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def summary_history_session(
        self,
        llm_service,
        db_session: AsyncSession,
        session_id: uuid.UUID,
        user_id: uuid.UUID,
        request,
        chat_messages: list,
        mode: str = "summary_session",
    ) -> str:
        try:
            if mode == "summary_session":
                summary_system_prompt = "Summarize the user's question from the chat history in 5–6 Japanese words. Keep it short, clear, and suitable for display as a sidebar session title."
            elif mode == "summary_conversation":
                summary_system_prompt = "Summarize chat history conversation"

            messages = [
                {
                    "role": "system",
                    "content": summary_system_prompt,
                },
                {"role": "user", "content": " ".join([(mess + "\n") for mess in chat_messages])},
            ]
            response = await llm_service.openai_client.chat.completions.create(
                model=request.model,
                messages=messages,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
            )
            summary_session = response.choices[0].message.content
            if mode == "summary_session":
                await self.chat_session_repository.update_session_summary(db_session, user_id, session_id, summary_session)
            elif mode == "summary_conversation":
                await self.chat_session_repository.update_session_conversation(db_session, user_id, session_id,
                                                                          summary_session)
                await self.chat_conversation_repository.update_conversation_summary(db_session, session_id)

            return summary_session
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def get_summary_history_session(
        self,
        db_session: AsyncSession,
        session_id: uuid.UUID,
    ):
        try:
            summary_session = await self.chat_session_repository.get_summary_session_by_user(db_session, session_id)
            return summary_session
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def delete_session(self, db_session: AsyncSession, user_id: uuid.UUID, session_id: uuid.UUID):
        await self.chat_session_repository.delete_session(db_session, user_id, session_id)

    async def handle_long_history_session(
        self,
        llm_service,
        db_session: AsyncSession,
        session_id: uuid.UUID,
        user_id: uuid.UUID,
        request,
        chat_history,
    ):
        history_text = "\n".join([f"{msg.role}: {msg.content}" for msg in chat_history])
        total_text = history_text + f"\nuser: {request.question}"
        total_token = self.count_tokens(total_text)
        if total_token > int(decouple.config("MAX_TOKENS_LENGTH")):
            messages_to_summarize = chat_history[:-10] if len(chat_history) > 5 else chat_history
            chat_message = [msg.content for i, msg in enumerate(messages_to_summarize)]
            summary_session = await self.summary_history_session(
                llm_service=llm_service,
                db_session=db_session,
                session_id=session_id,
                user_id=user_id,
                request=request,
                chat_messages=chat_message,
                mode="summary_conversation",
            )
            return summary_session


    async def clean_old_sessions(self, db_session: AsyncSession, days_threshold: int = 30) -> int:
        try:
            count = await self.chat_session_repository.clean_old_sessions(db_session, days_threshold)
            await db_session.commit()
            return count
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)
