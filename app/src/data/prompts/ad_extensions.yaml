description: |
  そのうえで、ユーザーのリクエストに「広告拡張も含めて」や「オプションも出して」などが含まれている場合でも、以下の「広告表示オプション（広告の拡張情報）」は**検索広告**または**P-Max**のときのみ出力してください。
  その他のプラットフォームでは、広告拡張情報を**出力しないでください。
extensions:
  - name: サイトリンク／クイックリンク
    fields:
      - name: 見出し
        description: 
        max_chars: 25
      - name: 説明文
        description: 
        max_chars: 35
      - name: URL
        description: 
  - name: コールアウト／テキスト補足
    fields:
      - name: テキスト
        description: max 6
        max_chars: 25
  - name: 構造化スニペット／カテゴリ補足
    fields:
      - name: ヘッダー
        description:  スタイル、ブランド
      - name: テキスト
        description: max 10
        max_chars: 25
  - name: 電話番号表示（任意）
    fields:
      - name: 表示する電話番号
        description: 
  - name: 価格表示オプション（Googleのみ）
    fields:
      - name: ヘッダー
        description: 
        max_chars: 25
      - name: 説明文
        description: 
        max_chars: 25
      - name: 価格
        description: 単位なし
      - name: URL
        description: 
  - name: プロモーション（Googleのみ）
    fields:
      - name: 年間行事
        description:  クリスマス
      - name: プロモーションタイプ
        description:  割引率、割引額
      - name: プロモーションの詳細
        description: 条件、開始日、終了日など
notes: |
  ユーザーの最初の入力に「広告拡張も含めて」「オプションも出して」などが含まれている場合は、広告本体と拡張を**まとめて出力**してください。
  広告拡張（広告表示オプション）は**検索広告**または**P-Max**に対してのみ出力してください。
  それ以外のプラットフォーム（LINE、Meta、YouTubeなど）には出力しないでください。
  それ以外の場合は、広告本体だけ出力し、最後に以下のように尋ねてください：
  「サイトリンクなどの広告表示オプションも作成しますか？」