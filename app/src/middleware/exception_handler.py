from fastapi import Request
from fastapi.responses import JSONResponse
from fastapi_base.exception import BusinessException
from loguru import logger


async def business_exception_handler(_: Request, exc: BusinessException) -> JSONResponse:
    logger.error(f"{exc.message}\n{exc.data}".rstrip())
    headers = {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Credentials": "true"}

    return JSONResponse(
        status_code=exc.status_code,
        content={"code": exc.code, "message": exc.message, "data": exc.data},
        headers=headers
    )
